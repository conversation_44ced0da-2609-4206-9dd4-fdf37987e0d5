import React, { useState } from 'react';
import { Meteor } from 'meteor/meteor';
import { ImagesCollection } from '/imports/api/uploads';
import { EditProfileModal } from './EditProfileModal.jsx';

export const CoverPhotoSection = ({ profileUser, displayName, userInitials, isOwnProfile, onThemeToggle, currentTheme }) => {
  const [showCoverUpload, setShowCoverUpload] = useState(false);
  const [showProfileUpload, setShowProfileUpload] = useState(false);
  const [showEditProfile, setShowEditProfile] = useState(false);
  const [uploadingCover, setUploadingCover] = useState(false);
  const [uploadingProfile, setUploadingProfile] = useState(false);

  const handleCoverPhotoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setUploadingCover(true);

      const upload = ImagesCollection.insert({
        file: file
      }, false);

      upload.on('start', function () {
        console.log('Cover photo upload started');
      });

      upload.on('end', function (error, fileObj) {
        setUploadingCover(false);
        if (error) {
          console.error('Cover photo upload error:', error);
          alert('Error uploading cover photo: ' + error.reason);
        } else {
          console.log('Cover photo uploaded successfully');
          // Update user's cover photo
          Meteor.call('images.updateCoverPhoto', fileObj._id, (err) => {
            if (err) {
              console.error('Error updating cover photo:', err);
              alert('Error updating cover photo: ' + err.reason);
            } else {
              console.log('Cover photo updated successfully');
              setShowCoverUpload(false);
              // Reset file input
              event.target.value = '';
            }
          });
        }
      });

      upload.start();
    }
  };

  const handleProfilePictureUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setUploadingProfile(true);

      const upload = ImagesCollection.insert({
        file: file
      }, false);

      upload.on('start', function () {
        console.log('Profile picture upload started');
      });

      upload.on('end', function (error, fileObj) {
        setUploadingProfile(false);
        if (error) {
          console.error('Profile picture upload error:', error);
          alert('Error uploading profile picture: ' + error.reason);
        } else {
          console.log('Profile picture uploaded successfully');
          // Update user's profile picture
          Meteor.call('images.updateProfilePicture', fileObj._id, (err) => {
            if (err) {
              console.error('Error updating profile picture:', err);
              alert('Error updating profile picture: ' + err.reason);
            } else {
              console.log('Profile picture updated successfully');
              setShowProfileUpload(false);
              // Reset file input
              event.target.value = '';
            }
          });
        }
      });

      upload.start();
    }
  };

  return (
    <div className="cover-photo-section">
      {/* Cover Photo */}
      <div className="cover-photo">
        {profileUser.profile?.coverPhoto ? (
          <img
            src={profileUser.profile.coverPhoto}
            alt="Cover photo"
            className="cover-image"
          />
        ) : (
          <div className="cover-placeholder">
            <div className="cover-gradient"></div>
          </div>
        )}
        
        {isOwnProfile && (
          <div className="cover-actions">
            <button 
              className="btn-secondary cover-btn"
              onClick={() => setShowCoverUpload(true)}
            >
              📷 &nbsp;Edit Cover Photo
            </button>
          </div>
        )}
      </div>

      {/* Profile Info Overlay */}
      <div className="profile-header">
        <div className="profile-header-container">
          {/* Profile Picture */}
          <div className="profile-picture-container">
            <div className="profile-picture">
              {profileUser.profile?.profilePicture ? (
                <img
                  src={profileUser.profile.profilePicture}
                  alt={`${displayName}'s profile`}
                  className="profile-image"
                />
              ) : (
                <div className="profile-placeholder">
                  <span className="profile-initials">{userInitials}</span>
                </div>
              )}
              
              {isOwnProfile && (
                <button 
                  className="profile-picture-edit"
                  onClick={() => setShowProfileUpload(true)}
                  title="Change profile picture"
                >
                  📷
                </button>
              )}
            </div>
          </div>

          {/* User Info */}
          <div className="profile-info">
            <h1 className="profile-name">{displayName}</h1>
            <div className="profile-stats">
              <span className="stat">
                <strong>0</strong> friends
              </span>
              <span className="stat">
                <strong>0</strong> photos
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="profile-actions">
            {isOwnProfile ? (
              <>
                <button
                  className="btn-secondary"
                  onClick={() => setShowEditProfile(true)}
                >
                  ✏️ &nbsp;Edit Profile
                </button>
                <button
                  className="btn-secondary"
                  onClick={() => onThemeToggle()}
                >
                  {currentTheme === 'light' ? '🌙' : '☀️'} &nbsp;Theme
                </button>
              </>
            ) : (
              <>
                <button className="btn">
                  👋 Add Friend
                </button>
                <button className="btn-secondary">
                  💬 Message
                </button>
                <button className="btn-secondary">
                  ⋯ More
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Upload Modals */}
      {showCoverUpload && (
        <div className="upload-modal">
          <div className="upload-modal-content">
            <div className="upload-modal-header">
              <h3>Update Cover Photo</h3>
              <button 
                className="close-btn"
                onClick={() => setShowCoverUpload(false)}
              >
                ✕
              </button>
            </div>
            <div className="upload-modal-body">
              {uploadingCover ? (
                <div className="upload-progress">
                  <div className="upload-icon">⏳</div>
                  <p>Uploading cover photo...</p>
                  <small>Please wait while we process your image</small>
                </div>
              ) : (
                <>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleCoverPhotoUpload}
                    className="file-input"
                    id="cover-upload"
                  />
                  <label htmlFor="cover-upload" className="file-label">
                    <div className="upload-area">
                      <div className="upload-icon">📷</div>
                      <p>Click to select a cover photo</p>
                      <small>Recommended size: 1200x315 pixels</small>
                    </div>
                  </label>
                </>
              )}
            </div>
          </div>
          <div 
            className="upload-modal-backdrop"
            onClick={() => setShowCoverUpload(false)}
          ></div>
        </div>
      )}

      {showProfileUpload && (
        <div className="upload-modal">
          <div className="upload-modal-content">
            <div className="upload-modal-header">
              <h3>Update Profile Picture</h3>
              <button 
                className="close-btn"
                onClick={() => setShowProfileUpload(false)}
              >
                ✕
              </button>
            </div>
            <div className="upload-modal-body">
              {uploadingProfile ? (
                <div className="upload-progress">
                  <div className="upload-icon">⏳</div>
                  <p>Uploading profile picture...</p>
                  <small>Please wait while we process your image</small>
                </div>
              ) : (
                <>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleProfilePictureUpload}
                    className="file-input"
                    id="profile-upload"
                  />
                  <label htmlFor="profile-upload" className="file-label">
                    <div className="upload-area">
                      <div className="upload-icon">👤</div>
                      <p>Click to select a profile picture</p>
                      <small>Recommended size: 400x400 pixels</small>
                    </div>
                  </label>
                </>
              )}
            </div>
          </div>
          <div 
            className="upload-modal-backdrop"
            onClick={() => setShowProfileUpload(false)}
          ></div>
        </div>
      )}

      {/* Edit Profile Modal */}
      <EditProfileModal
        user={profileUser}
        isOpen={showEditProfile}
        onClose={() => setShowEditProfile(false)}
        onThemeToggle={onThemeToggle}
        currentTheme={currentTheme}
      />
    </div>
  );
};
