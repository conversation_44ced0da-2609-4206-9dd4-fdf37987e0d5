import { Meteor } from 'meteor/meteor';
import { Accounts } from 'meteor/accounts-base';
import { PostsCollection } from '/imports/api/posts';
import { ImagesCollection } from '/imports/api/uploads';
import { check } from 'meteor/check';
import 'meteor/accounts-password';
import './fixtures.js';

Meteor.startup(async () => {
  // Configure accounts
  Accounts.config({
    sendVerificationEmail: false,
    forbidClientAccountCreation: false,
  });  
});

// Methods
Meteor.methods({
  'users.updateBio'(bio) {
    check(bio, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to update your bio');
    }

    return Meteor.users.updateAsync(this.userId, {
      $set: {
        'profile.bio': bio
      }
    });
  },

  'users.updateProfile'(profileData) {
    check(profileData, {
      fullname: String,
      bio: String,
      location: String,
      work: String,
      education: String,
      dob: String
    });

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to update your profile');
    }

    // Validate fullname
    if (!profileData.fullname.trim()) {
      throw new Meteor.Error('invalid-data', 'Full name is required');
    }

    // Validate date format if provided
    if (profileData.dob && profileData.dob.length > 0) {
      const dateParts = profileData.dob.split('.');
      if (dateParts.length !== 3 || profileData.dob.length !== 10) {
        throw new Meteor.Error('invalid-data', 'Please enter date in dd.mm.yyyy format');
      }

      const day = parseInt(dateParts[0], 10);
      const month = parseInt(dateParts[1], 10);
      const year = parseInt(dateParts[2], 10);

      if (isNaN(day) || isNaN(month) || isNaN(year) ||
          day < 1 || day > 31 || month < 1 || month > 12 || year < 1900 || year > new Date().getFullYear()) {
        throw new Meteor.Error('invalid-data', 'Please enter a valid date');
      }
    }

    return Meteor.users.updateAsync(this.userId, {
      $set: {
        'profile.fullname': profileData.fullname.trim(),
        'profile.bio': profileData.bio.trim(),
        'profile.location': profileData.location.trim(),
        'profile.work': profileData.work.trim(),
        'profile.education': profileData.education.trim(),
        'profile.dob': profileData.dob.trim()
      }
    });
  }
});

// Publish user data
Meteor.publish("userData", function () {
  if (this.userId) {
    return Meteor.users.find(
      { _id: this.userId },
      { fields: { emails: 1, profile: 1 } }
    );
  } else {
    this.ready();
  }
});

// Publish all users for profile lookup
Meteor.publish("allUsers", function () {
  return Meteor.users.find({}, {
    fields: {
      emails: 1,
      profile: 1,
      createdAt: 1
    }
  });
});
