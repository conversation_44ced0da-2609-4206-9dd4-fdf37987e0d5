import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';
import { check } from 'meteor/check';

export const CoverPhotosCollection = new Mongo.Collection('coverPhotos');

if (Meteor.isServer) {
  // Publications
  Meteor.publish('userCoverPhotos', function(userId) {
    check(userId, String);
    return CoverPhotosCollection.find({ userId }, {
      sort: { createdAt: -1 }
    });
  });

  Meteor.publish('allCoverPhotos', function() {
    if (this.userId) {
      return CoverPhotosCollection.find({ userId: this.userId }, {
        sort: { createdAt: -1 }
      });
    } else {
      this.ready();
    }
  });
}

// Methods
Meteor.methods({
  'coverPhotos.insert': async function(imageId, filename) {
    check(imageId, String);
    check(filename, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to save cover photos');
    }

    const user = await Meteor.users.findOneAsync(this.userId);
    if (!user) {
      throw new Meteor.Error('user-not-found', 'User not found');
    }

    return await CoverPhotosCollection.insertAsync({
      userId: this.userId,
      imageId: imageId,
      filename: filename,
      userInfo: {
        fullname: user.profile?.fullname || user.emails?.[0]?.address,
        email: user.emails?.[0]?.address
      },
      createdAt: new Date()
    });
  },

  'coverPhotos.setActive': async function(coverPhotoId) {
    check(coverPhotoId, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to change cover photos');
    }

    // Find the selected cover photo
    const coverPhoto = await CoverPhotosCollection.findOneAsync({
      _id: coverPhotoId,
      userId: this.userId
    });

    if (!coverPhoto) {
      throw new Meteor.Error('cover-photo-not-found', 'Cover photo not found');
    }

    // Update user's current cover photo (save only filename)
    return await Meteor.users.updateAsync(this.userId, {
      $set: {
        'profile.coverPhoto': coverPhoto.filename
      }
    });
  },

  'coverPhotos.remove': async function(coverPhotoId) {
    check(coverPhotoId, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to remove cover photos');
    }

    // Find the cover photo
    const coverPhoto = await CoverPhotosCollection.findOneAsync({
      _id: coverPhotoId,
      userId: this.userId
    });

    if (!coverPhoto) {
      throw new Meteor.Error('cover-photo-not-found', 'Cover photo not found');
    }

    // Remove from collection
    return await CoverPhotosCollection.removeAsync(coverPhotoId);
  }
});
