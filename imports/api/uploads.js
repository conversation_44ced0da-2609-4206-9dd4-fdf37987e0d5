import { Meteor } from 'meteor/meteor';
import { FilesCollection } from 'meteor/ostrio:files';
import { check } from 'meteor/check';

// Create file collection for images
export const ImagesCollection = new FilesCollection({
  collectionName: 'Images',
  allowClientCode: false, // Disallow remove files from Client
  storagePath: () => {
    return `${process.env.PWD}/uploads/images`;
  },
  onBeforeUpload(file) {
    // Allow only images
    if (/png|jpg|jpeg|gif|webp/i.test(file.extension)) {
      // Check file size (max 10MB)
      if (file.size <= 10485760) {
        return true;
      } else {
        return 'File size cannot exceed 10MB';
      }
    } else {
      return 'Please upload an image file (PNG, JPG, JPEG, GIF, WebP)';
    }
  },
  onAfterUpload(fileRef) {
    // Move file to permanent location after upload
    console.log(`File uploaded: ${fileRef.name}`);
  }
});

if (Meteor.isServer) {
  // Publish files to client
  Meteor.publish('files.images.all', function () {
    return ImagesCollection.find().cursor;
  });

  // Server-side methods
  Meteor.methods({
    'images.updateCoverPhoto': async function(imageId) {
      check(imageId, String);

      if (!this.userId) {
        throw new Meteor.Error('not-authorized', 'You must be logged in to update your cover photo');
      }

      // Verify the image exists and belongs to this user
      const image = await ImagesCollection.findOneAsync(imageId);
      if (!image) {
        throw new Meteor.Error('image-not-found', 'Image not found');
      }

      // Update user's cover photo
      return await Meteor.users.updateAsync(this.userId, {
        $set: {
          'profile.coverPhoto': image.link()
        }
      });
    },

    'images.updateProfilePicture': async function(imageId) {
      check(imageId, String);

      if (!this.userId) {
        throw new Meteor.Error('not-authorized', 'You must be logged in to update your profile picture');
      }

      // Verify the image exists
      const image = await ImagesCollection.findOneAsync(imageId);
      if (!image) {
        throw new Meteor.Error('image-not-found', 'Image not found');
      }

      // Update user's profile picture
      return await Meteor.users.updateAsync(this.userId, {
        $set: {
          'profile.profilePicture': image.link()
        }
      });
    },

    async 'images.remove'(imageId) {
      check(imageId, String);

      if (!this.userId) {
        throw new Meteor.Error('not-authorized', 'You must be logged in to remove images');
      }

      // Find the image
      const image = await ImagesCollection.findOneAsync(imageId);
      if (!image) {
        throw new Meteor.Error('image-not-found', 'Image not found');
      }

      // Remove the image
      return ImagesCollection.remove(imageId);
    }
  });
}
