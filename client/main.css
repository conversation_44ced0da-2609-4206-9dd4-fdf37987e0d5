@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700;800;900&display=swap');

:root {
  --primary-color: #cc00cc;
  --primary-light: #e066e0;
  --primary-dark: #990099;
  --primary-lighter: #f5ccf5;
  --primary-gradient: linear-gradient(135deg, #cc00cc 0%, #e066e0 100%);
  --card-bg: linear-gradient(145deg, #ffffff 0%, #fafafa 100%);
  --card-shadow: 0 10px 40px rgba(204, 0, 204, 0.1);
  --card-hover-shadow: 0 20px 60px rgba(204, 0, 204, 0.15);
  --text-color: #1a1a1a;
  --text-secondary: #666666;
  --bg-color: #f8f9fa;
  --input-bg: #ffffff;
  --input-border: #e1e5e9;
  --input-focus: #cc00cc;
}

[data-theme='dark'] {
  --primary-color: #cc00cc;
  --primary-light: #e066e0;
  --primary-dark: #990099;
  --primary-lighter: #4d004d;
  --primary-gradient: linear-gradient(135deg, #cc00cc 0%, #e066e0 100%);
  --card-bg: linear-gradient(145deg, #1e1e1e 0%, #2a2a2a 100%);
  --card-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  --card-hover-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  --text-color: #ffffff;
  --text-secondary: #b0b0b0;
  --bg-color: #0f0f0f;
  --input-bg: #2a2a2a;
  --input-border: #404040;
  --input-focus: #e066e0;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Rubik', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
  overflow-x: hidden;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(204, 0, 204, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(224, 102, 224, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(204, 0, 204, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.theme-toggle {
  background: var(--card-bg);
  border: 2px solid var(--input-border);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--card-shadow);
}

.theme-toggle:hover {
  transform: scale(1.1) rotate(10deg);
  box-shadow: var(--card-hover-shadow);
}

/* Landing page theme toggle (fixed position) */
.landing .theme-toggle {
  position: fixed;
  top: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  font-size: 1.5rem;
  z-index: 1000;
}

.title {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 900;
  letter-spacing: -0.02em;
}

.title.brand {
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  margin: 0 0 0.3em 0;
  text-align: center;
  line-height: 0.9;
  padding: 1rem;
}

.tagline {
  margin-bottom: 3rem;
  font-size: clamp(1.1rem, 3vw, 1.4rem);
  text-align: center;
  color: var(--text-secondary);
  font-weight: 500;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.btn {
  background: var(--primary-gradient);
  color: #ffffff;
  border: none;
  padding: 16px 32px;
  cursor: pointer;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.02em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(204, 0, 204, 0.3);
  position: relative;
  overflow: hidden;
  width: 100%;
  text-transform: uppercase;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(204, 0, 204, 0.4);
}

.btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(204, 0, 204, 0.3);
}

.card {
  background: var(--card-bg);
  border: 1px solid var(--input-border);
  box-shadow: var(--card-shadow);
  padding: 2rem;
  margin-bottom: 2rem;
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  width: 100%;
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: var(--card-hover-shadow);
}

.card h2 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 2rem 0;
  text-align: center;
  color: var(--text-color);
  letter-spacing: -0.01em;
}

.error {
  color: #e00;
  margin-bottom: 1rem;
  text-align: center;
}

.landing {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  min-height: 100vh;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.forms {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  gap: 2rem;
}

.forms form {
  width: 100%;
  max-width: 420px;
}

label {
  display: block;
  margin-bottom: 1.5rem;
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.95rem;
  letter-spacing: 0.01em;
}

input {
  width: 100%;
  padding: 16px 20px;
  margin-top: 8px;
  border: 2px solid var(--input-border);
  border-radius: 10px;
  background: var(--input-bg);
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 400;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
}

input:focus {
  border-color: var(--input-focus);
  box-shadow: 0 0 0 3px rgba(204, 0, 204, 0.1);
  transform: translateY(-1px);
}

input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

input[name="dob"] {
  font-family: 'Rubik', monospace;
  letter-spacing: 0.5px;
}

.forget {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.95rem;
}

.forget a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.forget a:hover {
  color: var(--primary-light);
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .landing {
    padding: 1rem;
  }

  .title.brand {
    font-size: clamp(2.5rem, 10vw, 4rem);
  }

  .card {
    padding: 2rem;
    margin-bottom: 1.5rem;
  }

  .tagline {
    margin-bottom: 2rem;
    font-size: clamp(1rem, 4vw, 1.2rem);
  }
}

@media (max-width: 480px) {
  .card {
    padding: 1.5rem;
    border-radius: 10px;
  }

  input {
    padding: 14px 16px;
  }

  .btn {
    padding: 14px 24px;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.title.brand {
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.tagline {
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.card {
  animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

.theme-toggle {
  animation: fadeInScale 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.6s both;
}

/* ===== PROFILE SYSTEM STYLES ===== */

/* Brand styling for dashboard */
.brand {
  font-size: 28px;
  font-weight: 800;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  cursor: pointer;
  margin: 0;
}

/* Dashboard */
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.welcome-section {
  text-align: center;
  padding: 60px 20px;
}

.welcome-section h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--text-color);
}

.welcome-section p {
  font-size: 18px;
  color: var(--text-secondary);
  margin-bottom: 32px;
}

/* Profile Loading and Error States */
.profile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--input-border);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.profile-not-found {
  text-align: center;
  padding: 60px 20px;
  max-width: 500px;
  margin: 0 auto;
}

.profile-not-found h2 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--text-color);
}

.profile-not-found p {
  font-size: 16px;
  color: var(--text-secondary);
  margin-bottom: 32px;
}

/* User Profile Layout */
.user-profile {
  min-height: 100vh;
  min-width: 100vh;
  background: var(--bg-color);
}

/* Cover Photo Section */
.cover-photo-section {
  position: relative;
  background: var(--card-bg);
  border-bottom: 1px solid var(--input-border);
}

.cover-photo {
  position: relative;
  height: 320px;
  overflow: hidden;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  position: relative;
}

.cover-gradient {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  opacity: 0.8;
}

.cover-actions {
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.cover-btn {
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-color);
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cover-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
}

/* Profile Header */
.profile-header {
  position: relative;
  background: var(--card-bg);
  padding: 0 20px 20px;
}

.profile-header-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: flex-end;
  gap: 20px;
  position: relative;
  padding: 0 20px;
}

.profile-picture-container {
  position: relative;
  margin-top: -80px;
}

.profile-picture {
  position: relative;
  width: 160px;
  height: 160px;
  border-radius: 50%;
  border: 6px solid var(--card-bg);
  overflow: hidden;
  background: var(--input-bg);
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-placeholder {
  width: 100%;
  height: 100%;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-initials {
  font-size: 48px;
  font-weight: 700;
  color: white;
}

.profile-picture-edit {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--card-bg);
  border: 2px solid var(--input-border);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
}

.profile-picture-edit:hover {
  background: var(--primary-lighter);
  transform: scale(1.1);
}

.profile-info {
  flex: 1;
  padding-top: 20px;
}

.profile-name {
  font-size: 36px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: var(--text-color);
}

.profile-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
}

.stat {
  color: var(--text-secondary);
  font-size: 14px;
}

.stat strong {
  color: var(--text-color);
  font-weight: 600;
}

.profile-bio-preview {
  color: var(--text-secondary);
  font-size: 16px;
  margin: 0;
  max-width: 500px;
}

.profile-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  padding-top: 20px;
}

.btn-secondary {
  background: var(--input-bg);
  color: var(--text-color);
  border: 1px solid var(--input-border);
  padding: 8px 15px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.btn-secondary:hover {
  background: var(--primary-lighter);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* Profile Navigation */
.profile-nav {
  background: var(--card-bg);
  border-bottom: 1px solid var(--input-border);
}

.profile-nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.profile-tabs {
  display: flex;
  gap: 0;
}

.tab {
  background: none;
  border: none;
  padding: 16px 24px;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.tab:hover {
  color: var(--text-color);
  background: var(--primary-lighter);
}

.tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

/* Profile Content Layout */
.profile-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  display: grid;
  grid-template-columns: 40% 60%;
  gap: 20px;
}

.profile-sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.profile-main {
  min-height: 600px;
  width: 100%;
}

/* Bio Section */
.bio-section {
  width: 100%;
}

.bio-section .card {
  margin-bottom: 20px;
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--input-border);
}

.card-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-color);
}

.btn-text {
  background: none;
  border: none;
  color: var(--primary-color);
  font-weight: 500;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.btn-text:hover {
  background: var(--primary-lighter);
}

.bio-edit {
  margin-bottom: 16px;
}

.bio-textarea {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  background: var(--input-bg);
  color: var(--text-color);
}

.bio-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(204, 0, 204, 0.1);
}

.bio-edit-actions {
  margin-top: 12px;
}

.char-count {
  color: var(--text-secondary);
  font-size: 12px;
}

.bio-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.bio-text {
  color: var(--text-color);
  line-height: 1.5;
  margin: 0;
}

.bio-empty {
  color: var(--text-secondary);
  font-style: italic;
  margin: 0;
}

.profile-details {
  margin-top: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid var(--input-border);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-icon {
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-content strong {
  font-weight: 600;
  color: var(--text-color);
  font-size: 14px;
}

.detail-content span {
  color: var(--text-secondary);
  font-size: 14px;
}

/* Stats Card */
.stats-card {
  background: var(--card-bg);
  width: 100%;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: var(--input-bg);
  border-radius: 8px;
  border: 1px solid var(--input-border);
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Photos Section */
.photos-section .card {
  width: 100%;
}

.photos-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.photos-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-top: 16px;
}

.photos-grid.expanded {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.photo-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.photo-item:hover {
  transform: scale(1.02);
  box-shadow: var(--card-hover-shadow);
}

.photo-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.photo-item:hover .photo-overlay {
  opacity: 1;
}

.photo-date {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.no-photos {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.no-photos-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-photos p {
  margin: 0 0 20px 0;
  font-size: 16px;
}

/* Upload Modals */
.upload-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.upload-modal-content {
  position: relative;
  background: var(--card-bg);
  border-radius: 10px;
  box-shadow: var(--card-hover-shadow);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  z-index: 1001;
}

.upload-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--input-border);
}

.upload-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-color);
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: var(--text-secondary);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: var(--input-border);
  color: var(--text-color);
}

.upload-modal-body {
  padding: 20px;
}

.file-input {
  display: none;
}

.file-label {
  display: block;
  cursor: pointer;
}

.upload-area {
  border: 2px dashed var(--input-border);
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  transition: all 0.2s ease;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background: var(--primary-lighter);
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.upload-area p {
  margin: 0 0 8px 0;
  font-weight: 500;
  color: var(--text-color);
}

.upload-area small {
  color: var(--text-secondary);
  font-size: 14px;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.upload-progress .upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: pulse 1.5s ease-in-out infinite;
}

.upload-progress p {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.upload-progress small {
  color: var(--text-secondary);
  font-size: 14px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Lightbox */
.lightbox {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.9);
}

.lightbox-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.lightbox-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.lightbox-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 20px;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
}

.lightbox-close:hover {
  background: rgba(0, 0, 0, 0.8);
}

.lightbox-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
}

.lightbox-nav:hover {
  background: rgba(0, 0, 0, 0.8);
}

.lightbox-prev {
  left: 20px;
}

.lightbox-next {
  right: 20px;
}

.lightbox-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lightbox-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.lightbox-info {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  text-align: center;
}

.lightbox-date {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.lightbox-caption {
  font-size: 16px;
  line-height: 1.4;
}

/* Posts Wall */
.posts-section {
  width: 100%;
}

.no-posts {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.no-posts-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.no-posts h3 {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: var(--text-color);
}

.no-posts p {
  margin: 0;
  font-size: 16px;
}

.posts-wall {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.post-card {
  background: var(--card-bg);
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: all 0.2s ease;
  width: 100%;
}

.post-card:hover {
  box-shadow: var(--card-hover-shadow);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--input-border);
}

.post-author {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  color: var(--text-color);
  font-size: 14px;
}

.post-time {
  color: var(--text-secondary);
  font-size: 12px;
}

.post-action-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.post-action-btn:hover {
  background: var(--input-border);
  color: var(--text-color);
}

.post-content {
  padding: 16px 20px;
}

.post-text {
  margin: 0 0 16px 0;
  line-height: 1.5;
  color: var(--text-color);
}

.post-images {
  border-radius: 8px;
  overflow: hidden;
  display: grid;
  gap: 4px;
}

.post-images.single {
  grid-template-columns: 1fr;
}

.post-images.multiple {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.post-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  cursor: pointer;
  transition: all 0.2s ease;
}

.post-image:hover {
  transform: scale(1.02);
}

.post-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  border-bottom: 1px solid var(--input-border);
  font-size: 14px;
}

.like-count {
  color: var(--text-secondary);
}

.comment-count {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 14px;
}

.comment-count:hover {
  color: var(--primary-color);
}

.post-interactions {
  display: flex;
  padding: 8px 12px;
  border-bottom: 1px solid var(--input-border);
}

.interaction-btn {
  flex: 1;
  background: none;
  border: none;
  padding: 12px;
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 14px;
}

.interaction-btn:hover {
  background: var(--input-border);
  color: var(--text-color);
}

.interaction-btn.liked {
  color: var(--primary-color);
}

/* Comments */
.comments-section {
  padding: 16px 20px;
}

.comment {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-bubble {
  background: var(--input-bg);
  border-radius: 10px;
  padding: 8px 12px;
  margin-bottom: 4px;
}

.comment-author {
  font-weight: 600;
  font-size: 13px;
  color: var(--text-color);
  margin-bottom: 2px;
}

.comment-text {
  font-size: 14px;
  color: var(--text-color);
  line-height: 1.3;
}

.comment-actions {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--text-secondary);
}

.comment-time {
  cursor: pointer;
}

.comment-delete {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 12px;
}

.comment-delete:hover {
  color: var(--primary-color);
}

.add-comment {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.comment-input-container {
  flex: 1;
  display: flex;
  gap: 8px;
}

.comment-input {
  flex: 1;
  background: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: 10px;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--text-color);
}

.comment-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.comment-submit {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.comment-submit:hover {
  background: var(--primary-dark);
}

.comment-submit:disabled {
  background: var(--input-border);
  cursor: not-allowed;
}

/* New Post Form */
.new-post-form {
  margin-bottom: 20px;
  width: 100%;
}

.new-post-form .card {
  width: 100%;
}

.post-form-header {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  padding: 16px 20px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.post-form-input {
  flex: 1;
}

.post-textarea {
  width: 100%;
  border: none;
  background: var(--input-bg);
  color: var(--text-color);
  font-family: inherit;
  font-size: 16px;
  resize: none;
  border-radius: 10px;
  padding: 12px 16px;
  transition: all 0.2s ease;
}

.post-textarea:focus {
  outline: none;
  background: var(--card-bg);
  box-shadow: 0 0 0 2px var(--primary-color);
}

.post-textarea.expanded {
  border-radius: 10px;
  min-height: 100px;
}

.image-preview {
  padding: 0 20px;
  margin-bottom: 16px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
}

.image-preview-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
}

.image-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.post-error {
  padding: 0 20px;
  margin-bottom: 16px;
}

.post-form-actions {
  padding: 16px 20px;
  border-top: 1px solid var(--input-border);
}

.post-options {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.post-option {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 14px;
}

.post-option:hover:not(:disabled) {
  background: var(--input-border);
  color: var(--text-color);
}

.post-option:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.option-icon {
  font-size: 16px;
}

.option-text {
  font-weight: 500;
}

.post-form-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.privacy-select {
  background: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 14px;
  color: var(--text-color);
  cursor: pointer;
}

.submit-buttons {
  display: flex;
  gap: 8px;
}

.character-count {
  text-align: right;
  color: var(--text-secondary);
}

/* About Section */
.about-section .card {
  padding: 20px;
  width: 100%;
}

.about-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.about-item {
  padding-bottom: 16px;
  border-bottom: 1px solid var(--input-border);
}

.about-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.about-item strong {
  display: block;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.about-item p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Responsive Design for Profile */
@media (max-width: 1024px) {
  .profile-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .profile-sidebar {
    order: 2;
  }

  .profile-main {
    order: 1;
  }
}

@media (max-width: 768px) {
  .brand {
    font-size: 24px;
  }

  .cover-photo {
    height: 200px;
  }

  .profile-header-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 16px;
  }

  .profile-picture-container {
    margin-top: -60px;
  }

  .profile-picture {
    width: 120px;
    height: 120px;
  }

  .profile-initials {
    font-size: 36px;
  }

  .profile-name {
    font-size: 28px;
  }

  .profile-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .profile-content {
    padding: 16px;
  }

  .profile-tabs {
    overflow-x: auto;
  }

  .tab {
    white-space: nowrap;
    padding: 12px 20px;
  }

  .photos-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .photos-grid.expanded {
    grid-template-columns: repeat(2, 1fr);
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }

  .stat-item {
    padding: 12px 8px;
  }

  .stat-number {
    font-size: 18px;
  }

  .stat-label {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .profile-content {
    padding: 12px;
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .profile-header {
    padding: 0 12px 16px;
  }

  .profile-header-container {
    padding: 0 12px;
  }

  .profile-nav-container {
    padding: 0 12px;
  }

  .cover-actions {
    bottom: 12px;
    right: 12px;
  }

  .cover-btn {
    padding: 8px 12px;
    font-size: 14px;
  }

  .upload-modal-content {
    width: 95%;
  }

  .lightbox-nav {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .lightbox-prev {
    left: 10px;
  }

  .lightbox-next {
    right: 10px;
  }

  .post-options {
    flex-wrap: wrap;
    gap: 8px;
  }

  .post-option {
    padding: 6px 8px;
    font-size: 13px;
  }
}

/* Edit Profile Modal */
.edit-profile-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.edit-profile-modal .modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.edit-profile-modal .modal-content {
  position: relative;
  background: var(--card-bg);
  border-radius: 10px;
  box-shadow: var(--card-hover-shadow);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.edit-profile-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--input-border);
  flex-shrink: 0;
}

.edit-profile-modal .modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-color);
}

.edit-profile-form {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  padding-bottom: 8px;
  border-bottom: 1px solid var(--input-border);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 6px;
  font-size: 14px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-family: inherit;
  font-size: 14px;
  background: var(--input-bg);
  color: var(--text-color);
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(204, 0, 204, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.char-count {
  display: block;
  text-align: right;
  color: var(--text-secondary);
  font-size: 12px;
  margin-top: 4px;
}

.theme-selector {
  display: flex;
  gap: 8px;
}

.theme-option {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid var(--input-border);
  border-radius: 8px;
  background: var(--input-bg);
  color: var(--text-color);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.theme-option:hover {
  border-color: var(--primary-color);
  background: var(--primary-lighter);
}

.theme-option.active {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
}

.form-error {
  margin-bottom: 20px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid var(--input-border);
  margin-top: 20px;
}

.action-group {
  display: flex;
  gap: 12px;
}

.danger-zone {
  display: flex;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-danger:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* Album Section */
.album-section {
  margin-bottom: 2rem;
}

.album-count {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.album-content {
  padding: 0;
}

.album-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 20px;
}

.album-item {
  position: relative;
  aspect-ratio: 16/9;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.album-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-hover-shadow);
}

.album-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.album-item:hover .album-thumbnail {
  transform: scale(1.05);
}

.album-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  opacity: 0;
  transition: opacity 0.2s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 12px;
}

.album-item:hover .album-overlay {
  opacity: 1;
}

.album-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.album-date {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.album-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.album-action-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.album-action-btn:hover {
  background: white;
  transform: scale(1.1);
}

.album-action-btn.delete:hover {
  background: #ff4757;
  color: white;
}

.album-action-btn.set-cover:hover {
  background: var(--primary-color);
  color: white;
}

.no-album-photos {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.no-album-photos .no-photos-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-album-photos p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 16px;
  max-width: 400px;
}

.album-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--text-secondary);
}

/* Delete Modal */
.delete-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.delete-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.delete-modal-content {
  position: relative;
  background: var(--card-bg);
  border-radius: 10px;
  box-shadow: var(--card-hover-shadow);
  max-width: 400px;
  width: 100%;
  padding: 24px;
  text-align: center;
}

.delete-modal-content h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-color);
}

.delete-modal-content p {
  margin: 0 0 24px 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

.delete-modal-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* Responsive adjustments for edit modal */
@media (max-width: 768px) {
  .edit-profile-modal {
    padding: 10px;
  }

  .edit-profile-modal .modal-content {
    max-width: 100%;
    max-height: 95vh;
  }

  .edit-profile-modal .modal-header {
    padding: 16px;
  }

  .edit-profile-form {
    padding: 16px;
  }

  .form-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-group {
    order: 2;
  }

  .danger-zone {
    order: 1;
    justify-content: center;
  }

  .album-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
    padding: 16px;
  }

  .delete-modal-content {
    padding: 20px;
  }

  .delete-modal-actions {
    flex-direction: column;
  }
}
