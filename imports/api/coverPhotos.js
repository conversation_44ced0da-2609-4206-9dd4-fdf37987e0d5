import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';
import { check } from 'meteor/check';

export const CoverPhotosCollection = new Mongo.Collection('coverPhotos');

if (Meteor.isServer) {
  // Publications
  Meteor.publish('userCoverPhotos', function(userId) {
    check(userId, String);
    return CoverPhotosCollection.find({ userId }, {
      sort: { createdAt: -1 }
    });
  });

  Meteor.publish('allCoverPhotos', function() {
    if (this.userId) {
      return CoverPhotosCollection.find({ userId: this.userId }, {
        sort: { createdAt: -1 }
      });
    } else {
      this.ready();
    }
  });
}

// Methods
Meteor.methods({
  'coverPhotos.insert': async function(imageId, imageUrl) {
    check(imageId, String);
    check(imageUrl, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to save cover photos');
    }

    const user = await Meteor.users.findOneAsync(this.userId);
    if (!user) {
      throw new Meteor.Error('user-not-found', 'User not found');
    }

    return await CoverPhotosCollection.insertAsync({
      userId: this.userId,
      imageId: imageId,
      imageUrl: imageUrl,
      userInfo: {
        fullname: user.profile?.fullname || user.emails?.[0]?.address,
        email: user.emails?.[0]?.address
      },
      createdAt: new Date(),
      isActive: true // Mark as current cover photo
    });
  },

  'coverPhotos.setActive': async function(coverPhotoId) {
    check(coverPhotoId, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to change cover photos');
    }

    // First, set all user's cover photos to inactive
    await CoverPhotosCollection.updateAsync(
      { userId: this.userId },
      { $set: { isActive: false } },
      { multi: true }
    );

    // Then set the selected one as active
    const result = await CoverPhotosCollection.updateAsync(coverPhotoId, {
      $set: { isActive: true }
    });

    if (result) {
      // Update user's current cover photo
      const coverPhoto = await CoverPhotosCollection.findOneAsync(coverPhotoId);
      if (coverPhoto) {
        await Meteor.users.updateAsync(this.userId, {
          $set: {
            'profile.coverPhoto': coverPhoto.imageUrl
          }
        });
      }
    }

    return result;
  },

  'coverPhotos.remove': async function(coverPhotoId) {
    check(coverPhotoId, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to remove cover photos');
    }

    // Find the cover photo
    const coverPhoto = await CoverPhotosCollection.findOneAsync({
      _id: coverPhotoId,
      userId: this.userId
    });

    if (!coverPhoto) {
      throw new Meteor.Error('cover-photo-not-found', 'Cover photo not found');
    }

    // Remove from collection
    return await CoverPhotosCollection.removeAsync(coverPhotoId);
  }
});
