import React, { useState } from 'react';
import { useTracker } from 'meteor/react-meteor-data';
import { Meteor } from 'meteor/meteor';
import { CoverPhotosCollection } from '/imports/api/coverPhotos';

export const AlbumSection = ({ profileUser, isOwnProfile }) => {
  const [selectedPhoto, setSelectedPhoto] = useState(null);
  const [showConfirmDelete, setShowConfirmDelete] = useState(null);

  // Subscribe to cover photos
  const { coverPhotos, isLoading } = useTracker(() => {
    if (!profileUser) {
      return { coverPhotos: [], isLoading: true };
    }

    const sub = Meteor.subscribe('userCoverPhotos', profileUser._id);
    
    const coverPhotos = CoverPhotosCollection.find(
      { userId: profileUser._id },
      { sort: { createdAt: -1 } }
    ).fetch();

    return {
      coverPhotos,
      isLoading: !sub.ready()
    };
  }, [profileUser?._id]);

  const handleSetAsCoverPhoto = (coverPhotoId) => {
    Meteor.call('coverPhotos.setActive', coverPhotoId, (error) => {
      if (error) {
        console.error('Error setting cover photo:', error);
        alert('Error setting cover photo: ' + error.reason);
      } else {
        console.log('Cover photo updated successfully');
      }
    });
  };

  const handleDeletePhoto = (coverPhotoId) => {
    Meteor.call('coverPhotos.remove', coverPhotoId, (error) => {
      if (error) {
        console.error('Error deleting cover photo:', error);
        alert('Error deleting cover photo: ' + error.reason);
      } else {
        console.log('Cover photo deleted successfully');
        setShowConfirmDelete(null);
      }
    });
  };

  const openLightbox = (photo) => {
    setSelectedPhoto(photo);
  };

  const closeLightbox = () => {
    setSelectedPhoto(null);
  };

  const navigatePhoto = (direction) => {
    const currentIndex = coverPhotos.findIndex(photo => photo._id === selectedPhoto._id);
    let newIndex;
    
    if (direction === 'next') {
      newIndex = (currentIndex + 1) % coverPhotos.length;
    } else {
      newIndex = currentIndex === 0 ? coverPhotos.length - 1 : currentIndex - 1;
    }
    
    setSelectedPhoto(coverPhotos[newIndex]);
  };

  if (isLoading) {
    return (
      <div className="album-section">
        <div className="card">
          <div className="card-header">
            <h3>Cover Photo Album</h3>
          </div>
          <div className="album-loading">
            <p>Loading album...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="album-section">
      <div className="card">
        <div className="card-header">
          <h3>Cover Photo Album</h3>
          <span className="album-count">
            {coverPhotos.length} {coverPhotos.length === 1 ? 'photo' : 'photos'}
          </span>
        </div>

        <div className="album-content">
          {coverPhotos.length > 0 ? (
            <div className="album-grid">
              {coverPhotos.map((photo) => (
                <div
                  key={photo._id}
                  className="album-item"
                  onClick={() => openLightbox(photo)}
                >
                  <img 
                    src={photo.imageUrl} 
                    alt="Cover photo"
                    className="album-thumbnail"
                  />
                  <div className="album-overlay">
                    <div className="album-info">
                      <div className="album-date">
                        {new Date(photo.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                    {isOwnProfile && (
                      <div className="album-actions">
                        <button
                          className="album-action-btn set-cover"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSetAsCoverPhoto(photo._id);
                          }}
                          title="Set as cover photo"
                        >
                          📷
                        </button>
                        <button
                          className="album-action-btn delete"
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowConfirmDelete(photo._id);
                          }}
                          title="Delete photo"
                        >
                          🗑️
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-album-photos">
              <div className="no-photos-icon">📷</div>
              <p>
                {isOwnProfile 
                  ? "No cover photos yet. Upload your first cover photo to start building your album!" 
                  : "No cover photos to show"
                }
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Photo Lightbox */}
      {selectedPhoto && (
        <div className="lightbox">
          <div className="lightbox-content">
            <button 
              className="lightbox-close"
              onClick={closeLightbox}
            >
              ✕
            </button>
            
            {coverPhotos.length > 1 && (
              <>
                <button 
                  className="lightbox-nav lightbox-prev"
                  onClick={() => navigatePhoto('prev')}
                >
                  ‹
                </button>
                <button 
                  className="lightbox-nav lightbox-next"
                  onClick={() => navigatePhoto('next')}
                >
                  ›
                </button>
              </>
            )}

            <div className="lightbox-image-container">
              <img 
                src={selectedPhoto.imageUrl} 
                alt="Cover photo"
                className="lightbox-image"
              />
            </div>

            <div className="lightbox-info">
              <div className="lightbox-date">
                Uploaded on {new Date(selectedPhoto.createdAt).toLocaleDateString()}
              </div>
            </div>
          </div>
          <div 
            className="lightbox-backdrop"
            onClick={closeLightbox}
          ></div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showConfirmDelete && (
        <div className="delete-modal">
          <div className="delete-modal-content">
            <h3>Delete Cover Photo</h3>
            <p>Are you sure you want to delete this cover photo? This action cannot be undone.</p>
            <div className="delete-modal-actions">
              <button 
                className="btn-secondary"
                onClick={() => setShowConfirmDelete(null)}
              >
                Cancel
              </button>
              <button 
                className="btn-danger"
                onClick={() => handleDeletePhoto(showConfirmDelete)}
              >
                Delete
              </button>
            </div>
          </div>
          <div 
            className="delete-modal-backdrop"
            onClick={() => setShowConfirmDelete(null)}
          ></div>
        </div>
      )}
    </div>
  );
};
